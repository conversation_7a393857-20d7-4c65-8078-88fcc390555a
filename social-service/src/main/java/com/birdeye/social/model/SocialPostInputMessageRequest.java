package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.ToString;
import lombok.Data;
import lombok.ToString;
import com.birdeye.social.validation.ScheduleDateLimit;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class SocialPostInputMessageRequest implements Serializable {

    private static final long serialVersionUID = -5573490493935590043L;
    private Integer id;
    @ScheduleDateLimit
    private String scheduleDate;
    private boolean isCreatedOnBTP = false;
    private String postText;
    private List<MediaData> images;
    private List<String> compressedImages;
    private List<MediaData> videos;
    private String videoThumbnailUrl;
    private String videoThumbnailMetadata;
    @Valid
    private Map<String,PostingPageScheduler> postingSites;
    private boolean isRestrictedForMobile = false;
    private Date oldScheduledDate;
    private List<String> links;
    private Integer businessId;
    private ReviewPostingMetaData reviewMetaData;
    private Integer createdBy;
    private Integer editedBy;
    private List<MentionData> mentions;
    private List<String> mediaSequence;
    private String saveType;
    private Integer reviewId;
    private TemplateData templateData;
    private Boolean autoShare;
    private Integer templateId;
    private Integer approvalWorkflowId;
    private String activity;

    private Boolean isDuplicate;

    private Integer duplicatedPostId;

    private Boolean isQuotedTweet;

    private String quotedPostId;

    private Boolean isValidDraft = true;

    private Boolean isValidPreview;

    private CreatePostGroupDetails postGroupDetails;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String contentGenerated;

    private String quotedTweetSource;

    private Boolean aiPost;

    private Boolean isSameContent;

    public CreatePostGroupDetails getPostGroupDetails() {
        return postGroupDetails;
    }

    public void setPostGroupDetails(CreatePostGroupDetails postGroupDetails) {
        this.postGroupDetails = postGroupDetails;
    }

    @JsonProperty("isCreatedOnBTP")
    public boolean isCreatedOnBTP() {
        return isCreatedOnBTP;
    }

    public void setCreatedOnBTP(boolean createdOnBTP) {
        isCreatedOnBTP = createdOnBTP;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPostText() {
        return postText;
    }

    public void setPostText(String postText) {
        this.postText = postText;
    }

    public List<MediaData> getImages() {
        return images;
    }

    public void setImages(List<MediaData> images) {
        this.images = images;
    }

    public List<MediaData> getVideos() {
        return videos;
    }

    public void setVideos(List<MediaData> videos) {
        this.videos = videos;
    }


    public String getVideoThumbnailUrl() {
        return videoThumbnailUrl;
    }

    public void setVideoThumbnailUrl(String videoThumbnailUrl) {
        this.videoThumbnailUrl = videoThumbnailUrl;
    }

    public Map<String, PostingPageScheduler> getPostingSites() {
        return postingSites;
    }

    public void setPostingSites(Map<String, PostingPageScheduler> postingSites) {
        this.postingSites = postingSites;
    }

    public String getScheduleDate() {
        return scheduleDate;
    }

    public void setScheduleDate(String scheduleDate) {
        this.scheduleDate = scheduleDate;
    }

    public Date getOldScheduledDate() {
        return oldScheduledDate;
    }

    public void setOldScheduledDate(Date oldScheduledDate) {
        this.oldScheduledDate = oldScheduledDate;
    }

    public List<String> getLinks() {
        return links;
    }

    public void setLinks(List<String> links) {
        this.links = links;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public ReviewPostingMetaData getReviewMetaData() {
        return reviewMetaData;
    }

    public void setReviewMetaData(ReviewPostingMetaData reviewMetaData) {
        this.reviewMetaData = reviewMetaData;
    }

    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public Integer getEditedBy() {
        return editedBy;
    }

    public void setEditedBy(Integer editedBy) {
        this.editedBy = editedBy;
    }

    public List<MentionData> getMentions() {
        return mentions;
    }

    public void setMentions(List<MentionData> mentions) {
        this.mentions = mentions;
    }
    public List<String> getMediaSequence() {
        return mediaSequence;
    }

    public void setMediaSequence(List<String> mediaSequence) {
        this.mediaSequence = mediaSequence;
    }

    public String getSaveType() {
        return saveType;
    }

    public void setSaveType(String saveType) {
        this.saveType = saveType;
    }

    public Integer getReviewId() {
        return reviewId;
    }

    public void setReviewId(Integer reviewId) {
        this.reviewId = reviewId;
    }

    public Boolean getAutoShare() {
        return autoShare;
    }

    public void setAutoShare(Boolean autoShare) {
        this.autoShare = autoShare;
    }

    public TemplateData getTemplateData() {
        return templateData;
    }

    public void setTemplateData(TemplateData templateData) {
        this.templateData = templateData;
    }

    public Integer getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Integer templateId) {
        this.templateId = templateId;
    }

    public List<String> getCompressedImages() {
        return compressedImages;
    }

    public void setCompressedImages(List<String> compressedImages) {
        this.compressedImages = compressedImages;
    }

    public String getActivity() {
        return activity;
    }

    public void setActivity(String activity) {
        this.activity = activity;
    }

    public Boolean getIsDuplicate() {
        return isDuplicate;
    }

    public void setIsDuplicate(Boolean duplicate) {
        isDuplicate = duplicate;
    }

    public Integer getDuplicatedPostId() {
        return duplicatedPostId;
    }

    public void setDuplicatedPostId(Integer duplicatedPostId) {
        this.duplicatedPostId = duplicatedPostId;
    }

    public Boolean getIsQuotedTweet() {
        return isQuotedTweet;
    }

    public void setIsQuotedTweet(Boolean quotedTweet) {
        isQuotedTweet = quotedTweet;
    }

    public String getQuotedPostId() {
        return quotedPostId;
    }

    public void setQuotedPostId(String quotedPostId) {
        this.quotedPostId = quotedPostId;
    }

    public Boolean getIsValidDraft() {
        return this.isValidDraft;
    }

    public void setIsValidDraft(Boolean isValidDraft) {
        this.isValidDraft = isValidDraft;
    }
    public String getVideoThumbnailMetadata() {
        return videoThumbnailMetadata;
    }

    public void setVideoThumbnailMetadata(String videoThumbnailMetadata) {
        this.videoThumbnailMetadata = videoThumbnailMetadata;
    }

    public String getContentGenerated() {
        return contentGenerated;
    }

    public void setContentGenerated(String contentGenerated) {
        this.contentGenerated = contentGenerated;
    }

    public Boolean getValidPreview() {
        return isValidPreview;
    }

    public void setValidPreview(Boolean validPreview) {
        isValidPreview = validPreview;
    }

    public String getQuotedTweetSource() {
        return quotedTweetSource;
    }

    public void setQuotedTweetSource(String quotedTweetSource) {
        this.quotedTweetSource = quotedTweetSource;
    }

    public boolean isRestrictedForMobile() {
        return isRestrictedForMobile;
    }

    public void setRestrictedForMobile(boolean restrictedForMobile) {
        isRestrictedForMobile = restrictedForMobile;
    }

    public Boolean getAiPost() {
        return aiPost;
    }

    public void setAiPost(Boolean aiPost) {
        this.aiPost = aiPost;
    }

    public Boolean getIsSameContent() {return isSameContent;}

    public void setIsSameContent(Boolean isSameContent) {this.isSameContent = isSameContent;}

    public SocialPostInputMessageRequest deepCopy() {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String json = objectMapper.writeValueAsString(this);
            return objectMapper.readValue(json, SocialPostInputMessageRequest.class);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

}
