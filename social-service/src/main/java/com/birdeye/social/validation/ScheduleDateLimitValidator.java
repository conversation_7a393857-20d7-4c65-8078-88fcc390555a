package com.birdeye.social.validation;

import com.birdeye.social.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * Validator implementation for ScheduleDateLimit annotation.
 * Validates that the schedule date is not more than the specified number of days from current date.
 */
public class ScheduleDateLimitValidator implements ConstraintValidator<ScheduleDateLimit, String> {
    
    private static final Logger LOG = LoggerFactory.getLogger(ScheduleDateLimitValidator.class);
    
    // Date format used for schedule dates in the application
    private static final String SCHEDULE_DATE_FORMAT = "MM/dd/yyyy HH:mm:ss";
    
    private int maxDays;
    
    @Override
    public void initialize(ScheduleDateLimit constraintAnnotation) {
        this.maxDays = constraintAnnotation.maxDays();
    }
    
    @Override
    public boolean isValid(String scheduleDate, ConstraintValidatorContext context) {
        // If schedule date is null or empty, consider it valid (let other validations handle null checks)
        if (StringUtils.isEmpty(scheduleDate)) {
            return true;
        }
        
        try {
            // Parse the schedule date string
            SimpleDateFormat dateFormat = new SimpleDateFormat(SCHEDULE_DATE_FORMAT);
            Date scheduledDate = dateFormat.parse(scheduleDate);
            
            // Get current date
            Date currentDate = new Date();
            
            // Calculate the maximum allowed date (current date + maxDays)
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentDate);
            calendar.add(Calendar.DAY_OF_YEAR, maxDays);
            Date maxAllowedDate = calendar.getTime();
            
            // Check if scheduled date is within the allowed range
            boolean isValid = !scheduledDate.after(maxAllowedDate);
            
            if (!isValid) {
                LOG.warn("Schedule date validation failed. Schedule date: {}, Max allowed date: {}", 
                        scheduleDate, dateFormat.format(maxAllowedDate));
            }
            
            return isValid;
            
        } catch (ParseException e) {
            LOG.error("Failed to parse schedule date: {}. Expected format: {}", scheduleDate, SCHEDULE_DATE_FORMAT, e);
            // If we can't parse the date, let it pass and let other validations handle the format issue
            return true;
        } catch (Exception e) {
            LOG.error("Unexpected error during schedule date validation for date: {}", scheduleDate, e);
            // In case of unexpected errors, be lenient and let it pass
            return true;
        }
    }
}
