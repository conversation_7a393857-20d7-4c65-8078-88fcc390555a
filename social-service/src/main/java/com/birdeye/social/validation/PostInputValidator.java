package com.birdeye.social.validation;

import com.birdeye.social.constant.PostActivityType;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.model.PostingPageScheduler;
import com.birdeye.social.model.SocialPostInputMessageRequest;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.Set;

@Component
public class PostInputValidator implements Validator {

    private static String INSTAGRAM_MESSAGE = "Media must be present for posting on instagram";

    private static String YOUTUBE_MESSAGE = "Video must be present for posting on youtube";

    private static String FTLG_MESSAGE = "text or media must be present for posting";

    private static String GMB_VIDEO_MESSAGE = "video is not allowed for posting on google";

    private static String SCHEDULE_DATE_LIMIT_MESSAGE = "Schedule date cannot be more than 360 days from current date";

    // Date format used for schedule dates in the application
    private static final String SCHEDULE_DATE_FORMAT = "MM/dd/yyyy HH:mm:ss";

    // Maximum number of days allowed from current date
    private static final int MAX_SCHEDULE_DAYS = 360;

    @Override
    public boolean supports(Class<?> aClass) {
        return SocialPostInputMessageRequest.class.equals(aClass);
    }

    @Override
    public void validate(Object o, Errors errors) {
        SocialPostInputMessageRequest socialPostInputMessageRequest = (SocialPostInputMessageRequest) o;

        Map<String, PostingPageScheduler> postingPageSchedulerMap = socialPostInputMessageRequest.getPostingSites();

        if(PostActivityType.RESCHEDULED.getName().equals(socialPostInputMessageRequest.getActivity())) {
            return;
        }

        // Validate schedule date limit
        validateScheduleDateLimit(socialPostInputMessageRequest.getScheduleDate());

        if(MapUtils.isEmpty(postingPageSchedulerMap)) {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "posting sites cannot be null");
        }

        Set<String> channels = postingPageSchedulerMap.keySet();
        boolean isImagePresent = CollectionUtils.isNotEmpty(socialPostInputMessageRequest.getImages());
        boolean isVideoPresent = CollectionUtils.isNotEmpty(socialPostInputMessageRequest.getVideos());
        boolean isTextPresent = StringUtils.isNotEmpty(socialPostInputMessageRequest.getPostText());

        if(channels.contains(SocialChannel.INSTAGRAM.getName())) {
            if(!isImagePresent && !isVideoPresent) {
                throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INSTAGRAM_MESSAGE);
            }
        }
        if(channels.contains(SocialChannel.YOUTUBE.getName())) {
            if(!isVideoPresent) {
                throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, YOUTUBE_MESSAGE);
            }
        }
        if(channels.contains(SocialChannel.FACEBOOK.getName()) || channels.contains(SocialChannel.TWITTER.getName())
                || channels.contains(SocialChannel.LINKEDIN.getName()) || channels.contains(SocialChannel.GOOGLE.getName())) {
            if(!isImagePresent && !isVideoPresent && !isTextPresent) {
                throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, FTLG_MESSAGE);
            }
            if(channels.contains(SocialChannel.GOOGLE.getName()) && isVideoPresent) {
                throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, GMB_VIDEO_MESSAGE);
            }
        }
    }

    public void checkIfValidateAndValidate(Object request) {
        if(supports(request.getClass())) {
            validate(request, null);
        }
    }

    /**
     * Validates that the schedule date is not more than 360 days from current date.
     * @param scheduleDate the schedule date string to validate
     * @throws BirdeyeSocialException if the schedule date is more than 360 days from current date
     */
    private void validateScheduleDateLimit(String scheduleDate) {
        // If schedule date is null or empty, skip validation (let other validations handle null checks)
        if (StringUtils.isEmpty(scheduleDate)) {
            return;
        }

        try {
            // Parse the schedule date string
            SimpleDateFormat dateFormat = new SimpleDateFormat(SCHEDULE_DATE_FORMAT);
            Date scheduledDate = dateFormat.parse(scheduleDate);

            // Get current date
            Date currentDate = new Date();

            // Calculate the maximum allowed date (current date + MAX_SCHEDULE_DAYS)
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(currentDate);
            calendar.add(Calendar.DAY_OF_YEAR, MAX_SCHEDULE_DAYS);
            Date maxAllowedDate = calendar.getTime();

            // Check if scheduled date is within the allowed range
            if (scheduledDate.after(maxAllowedDate)) {
                throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, SCHEDULE_DATE_LIMIT_MESSAGE);
            }

        } catch (ParseException e) {
            // If we can't parse the date, let other validations handle the format issue
            // Don't throw an exception here to avoid masking other validation errors
        } catch (BirdeyeSocialException e) {
            // Re-throw our validation exception
            throw e;
        } catch (Exception e) {
            // In case of unexpected errors, log and continue
            // Don't fail the validation due to unexpected errors
        }
    }
}
