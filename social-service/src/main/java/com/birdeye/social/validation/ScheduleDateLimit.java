package com.birdeye.social.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Custom validation annotation to ensure that schedule date is not more than 360 days from current date.
 */
@Documented
@Constraint(validatedBy = ScheduleDateLimitValidator.class)
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ScheduleDateLimit {
    
    String message() default "Schedule date cannot be more than 360 days from current date";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    /**
     * Maximum number of days allowed from current date
     */
    int maxDays() default 360;
}
